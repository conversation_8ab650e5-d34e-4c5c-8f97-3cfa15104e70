# Portainer-EE Development Guide

## Quick Start for Daily Development

### Prerequisites Setup (One-time only)
If you haven't set up the development environment yet, follow the [initial setup guide](docs/dev/README.md).

### Daily Development Workflow

#### 1. Start Backend Development Server (Terminal 1)
```bash
# Navigate to server-ee directory
cd package/server-ee

# Set up environment variables
export PATH=$PATH:/usr/local/go/bin:/home/<USER>/go/bin

# Create data directory (if not exists)
mkdir -p /tmp/portainer-data

# Start backend with live reload
air -c .air.toml -- --data /tmp/portainer-data
```

**Backend will be available at:**
- HTTP: `http://localhost:9000`
- HTTPS: `https://localhost:9443`
- API Status: `http://localhost:9000/api/status`

#### 2. Start Frontend Development Server (Terminal 2)
```bash
# Navigate to server-ee directory (if not already there)
cd package/server-ee

# Start frontend with hot reload
yarn dev
```

**Frontend will be available at:**
- Development UI: `http://localhost:8999/`
- Network access: `http://***************:8999/` (your local IP)

#### 3. Development Features

**Backend Live Reload:**
- Monitors `api/` directory for Go file changes
- Automatically rebuilds and restarts server
- Debug logging enabled
- Changes reflect immediately

**Frontend Hot Reload:**
- Monitors `app/` directory for frontend changes
- Automatic browser refresh
- API calls automatically proxied to backend (`/api` → `http://localhost:9000`)
- Changes reflect immediately without page refresh

#### 4. Important Notes

- **Start Order:** Always start backend first, then frontend (backend has 5-minute timeout if no frontend connects)
- **Data Persistence:** Development data stored in `/tmp/portainer-data`
- **First Build:** Initial build may take longer due to dependency downloads
- **Subsequent Builds:** Much faster after first-time setup

#### 5. Stopping Servers
- Press `Ctrl+C` in each terminal to stop the respective server
- Both servers can run independently

#### 6. Troubleshooting

**If Air command not found:**
```bash
export PATH=$PATH:/usr/local/go/bin:/home/<USER>/go/bin
```

**If backend fails to start:**
```bash
# Check if data directory exists
mkdir -p /tmp/portainer-data

# Verify Go installation
go version
```

**If frontend fails to start:**
```bash
# Reinstall dependencies if needed
yarn install
```

---

## Advanced Development

### Building for Production
```bash
# Build server-ee
make server-ee

# Build specific components
cd package/server-ee && make all
```

### Running Tests
```bash
# Run all tests
make test

# Run server tests only
make test-server

# Run client tests only
cd package/server-ee && yarn test
```

### Linting
```bash
# Run all linters
make lint

# Run server linter only
make lint-server

# Run client linter only
cd package/server-ee && yarn lint
```

### Custom Make Targets

You can add custom make targets to the root Makefile by adding new *.make files in the `.dev` directory.
These should only include targets, please refer to the `.dev/example.make` file for reference.

### Live-reload development

Air (https://github.com/air-verse/air) is configured in each package to provide a live-reload development experience for Go code.

Here's an example of make targets that can be used to start a live-reload development session for the different packages:

```Makefile
# Edge configuration
EDGE_ID=$(shell uuidgen || echo "agent-dev")
EDGE_KEY=your-edge-key

# Server configuration
DATA_DIR=/tmp/portainer-data

dev-agent:
	cd package/agent && air -c .air.toml

dev-agent-edge:
	cd package/agent && air -c .air.toml -- --edge --edge-insecurepoll --edge-key $(EDGE_KEY) --edge-id $(EDGE_ID)

dev-ee:
	cd package/server-ee && air -c .air.toml -- --data $(DATA_DIR)

dev-ce:
	cd package/server-ce && air -c .air.toml -- --data $(DATA_DIR)
```

## Environment Variables

### Backend Environment Variables
```bash
# Required for Go and Air
export PATH=$PATH:/usr/local/go/bin:/home/<USER>/go/bin

# Optional: Set data directory
export DATA_DIR=/tmp/portainer-data

# Optional: Set log level
export LOG_LEVEL=DEBUG
```

### Frontend Environment Variables
The frontend development server will automatically load environment variables from `.env` files if present.

## Development URLs Summary

| Service | URL | Purpose |
|---------|-----|---------|
| Backend API (HTTP) | `http://localhost:9000` | Main API endpoint |
| Backend API (HTTPS) | `https://localhost:9443` | Secure API endpoint |
| Frontend Dev Server | `http://localhost:8999` | Development UI |
| API Status Check | `http://localhost:9000/api/status` | Health check |

## Quick Commands Reference

### One-liner to start both servers:
```bash
# Terminal 1 (Backend)
cd package/server-ee && export PATH=$PATH:/usr/local/go/bin:/home/<USER>/go/bin && mkdir -p /tmp/portainer-data && air -c .air.toml -- --data /tmp/portainer-data

# Terminal 2 (Frontend)
cd package/server-ee && yarn dev
```

### Check if servers are running:
```bash
# Check backend
curl http://localhost:9000/api/status

# Check frontend (should return HTML)
curl -I http://localhost:8999
```
