import { StateRegistry } from '@uirouter/angularjs';
import angular from 'angular';

export const modelsModule = angular
  .module('portainer.docker.react.views.models', [])
  .config(config).name;

/* @ngInject */
function config($stateRegistryProvider: StateRegistry) {
  $stateRegistryProvider.register({
    name: 'docker.models',
    url: '/models',
    views: {
      'content@': {
        templateUrl: '~@/docker/views/models/models.html',
        controller: 'ModelsController',
      },
    },
    data: {
      docs: '/user/docker/models',
    },
  });
}
