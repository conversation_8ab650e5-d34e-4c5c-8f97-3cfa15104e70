<page-header title="'Model list'" breadcrumbs="['Models']" reload="true"> </page-header>

<div class="row">
  <div class="col-lg-12 col-md-12 col-xs-12">
    <rd-widget>
      <rd-widget-header icon="brain" title-text="Docker Models"> </rd-widget-header>
      <rd-widget-body>
        <div class="col-sm-12 form-section-title">
          Docker AI Models
        </div>
        <div class="form-group">
          <div class="col-sm-12">
            <p class="text-muted small">
              Manage Docker AI models for machine learning and inference workloads.
            </p>
          </div>
        </div>
        
        <!-- Models table will be rendered here -->
        <div ng-if="models && models.length > 0">
          <table class="table table-hover nowrap-cells">
            <thead>
              <tr>
                <th>
                  <a ui-sref="docker.models" ng-click="order('Name')">
                    Name
                    <span ng-show="sortType == 'Name' && !sortReverse" class="glyphicon glyphicon-chevron-down"></span>
                    <span ng-show="sortType == 'Name' && sortReverse" class="glyphicon glyphicon-chevron-up"></span>
                  </a>
                </th>
                <th>
                  <a ui-sref="docker.models" ng-click="order('Size')">
                    Size
                    <span ng-show="sortType == 'Size' && !sortReverse" class="glyphicon glyphicon-chevron-down"></span>
                    <span ng-show="sortType == 'Size' && sortReverse" class="glyphicon glyphicon-chevron-up"></span>
                  </a>
                </th>
                <th>
                  <a ui-sref="docker.models" ng-click="order('Created')">
                    Created
                    <span ng-show="sortType == 'Created' && !sortReverse" class="glyphicon glyphicon-chevron-down"></span>
                    <span ng-show="sortType == 'Created' && sortReverse" class="glyphicon glyphicon-chevron-up"></span>
                  </a>
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="model in (models | filter:state.filter | orderBy:sortType:sortReverse)">
                <td>
                  <span class="monospace">{{ model.ModelName || model.Id }}</span>
                </td>
                <td>
                  <span>{{ model.Size | humansize }}</span>
                </td>
                <td>
                  <span>{{ model.Created | getisodate | date:'medium' }}</span>
                </td>
                <td>
                  <div class="btn-group" uib-dropdown>
                    <button type="button" class="btn btn-primary btn-sm" uib-dropdown-toggle>
                      Actions <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" role="menu">
                      <li><a ng-click="removeModel(model)">Remove</a></li>
                    </ul>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div ng-if="!models || models.length === 0">
          <div class="col-sm-12">
            <i class="fa fa-info-circle blue-icon" aria-hidden="true" style="margin-right: 2px;"></i>
            No models found.
          </div>
        </div>
      </rd-widget-body>
    </rd-widget>
  </div>
</div>
