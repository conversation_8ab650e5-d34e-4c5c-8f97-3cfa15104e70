angular.module('portainer.docker').controller('ModelsController', [
  '$scope',
  '$state',
  'Authentication',
  'Notifications',
  'HttpRequestHelper',
  'endpoint',
  function ($scope, $state, Authentication, Notifications, HttpRequestHelper, endpoint) {
    $scope.endpoint = endpoint;
    $scope.isAdmin = Authentication.isPureAdmin();

    $scope.state = {
      actionInProgress: false,
      filter: '',
    };

    $scope.sortType = 'Created';
    $scope.sortReverse = true;

    $scope.order = function (sortType) {
      $scope.sortReverse = $scope.sortType === sortType ? !$scope.sortReverse : false;
      $scope.sortType = sortType;
    };

    $scope.models = [];

    $scope.removeModel = function (model) {
      if (confirm('Are you sure you want to remove this model?')) {
        // TODO: Implement model removal
        Notifications.success('Model removal functionality will be implemented');
      }
    };

    function initView() {
      // TODO: Load models from API
      // For now, initialize with empty array
      $scope.models = [];
    }

    initView();
  },
]);
